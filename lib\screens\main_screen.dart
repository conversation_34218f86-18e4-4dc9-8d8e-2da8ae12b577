import 'package:flutter/material.dart';
import 'package:t2new/controllers/data_controller.dart';
import 'package:watch_it/watch_it.dart';

class MainScreen extends StatelessWidget with WatchItMixin {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final data = watchIt<DataController>();
    return Scaffold(
      appBar: AppBar(title: const Text('Todos')),
      body: ListView.builder(
        itemCount: data.todos.length,
        itemBuilder: (context, index) {
          return Card(
            margin: const EdgeInsets.all(8),
            child: ListTile(
              title: Text(data.todos[index].title ?? ''),
              subtitle: Text(data.todos[index].completed.toString()),
              trailing: Icon<PERSON>utton(
                icon: Icon(
                  data.todos[index].completed! ? Icons.check : Icons.close,
                ),
                onPressed: () {
                  di<DataController>().toggleTodo(data.todos[index].id!);
                },
              ),
            ),
          );
        },
      ),
    );
  }
}
