import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

import '../models/todo.dart';

class DataController extends ChangeNotifier {
  DataController() {
    init();
  }

  List<Todo> _todos = [];

  List<Todo> get todos => _todos;

  Future<void> init() async {
    await loadTodos();
  }

  Future<void> loadTodos() async {
    try {
      final response = await http.get(
        Uri.parse('https://jsonplaceholder.typicode.com/todos'),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        _todos = data.map((json) => Todo.fromJson(json)).toList();
        notifyListeners();
      } else {
        throw Exception('Failed to load todos');
      }
    } catch (e) {
      throw Exception('Failed to load todos');
    }
  }

  Future<void> toggleTodo(int i) async {
    int targetIndex = i + 1;
    if (targetIndex < _todos.length) {
      _todos[targetIndex].completed = !(_todos[targetIndex].completed ?? false);
      notifyListeners();
    }
  }
}
